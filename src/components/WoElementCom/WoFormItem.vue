<template>
  <div
    class="wo-form-item"
    :class="{
      'wo-form-item-arrow': arrow,
      'wo-form-item--filled': variant === 'filled',
      'wo-form-item--outlined': variant === 'outlined',
      'wo-form-item--small': size === 'small',
      'wo-form-item--large': size === 'large',
      'error': error,
      'disabled': disabled,
      'focused': isFocused
    }"
  >
    <van-field
      v-model="fieldValue"
      :label="label"
      :placeholder="placeholder"
      :readonly="readonly"
      :disabled="disabled"
      :required="required"
      :error="error"
      :error-message="errorMessage"
      :label-width="labelWidth"
      :label-align="labelAlign"
      :input-align="inputAlign"
      :type="type"
      :maxlength="maxlength"
      :rows="rows"
      :autosize="autosize"
      :clearable="clearable"
      :clickable="clickable"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @click="handleClick"
      @clear="handleClear"
    >
      <!-- 自定义左侧图标 -->
      <template #left-icon v-if="$slots.leftIcon">
        <slot name="leftIcon"></slot>
      </template>

      <!-- 自定义右侧图标 -->
      <template #right-icon v-if="$slots.rightIcon || arrow">
        <slot name="rightIcon">
          <img v-if="arrow" src="../../static/images/arrow-right-black.png" alt="箭头" class="arrow-icon">
        </slot>
      </template>

      <!-- 自定义输入框内容 -->
      <template #input v-if="$slots.input">
        <slot name="input"></slot>
      </template>

      <!-- 自定义标签内容 -->
      <template #label v-if="$slots.label">
        <slot name="label"></slot>
      </template>

      <!-- 自定义按钮 -->
      <template #button v-if="$slots.button">
        <slot name="button"></slot>
      </template>

      <!-- 自定义额外内容 -->
      <template #extra v-if="$slots.extra">
        <slot name="extra"></slot>
      </template>
    </van-field>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  // 原有属性
  label: {
    type: String,
    required: true
  },
  arrow: {
    type: Boolean,
    default: false
  },
  // 新增样式定制属性
  variant: {
    type: String,
    default: 'default', // default, filled, outlined
    validator: (value) => ['default', 'filled', 'outlined'].includes(value)
  },
  size: {
    type: String,
    default: 'medium', // small, medium, large
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  // Vant Field 相关属性
  modelValue: {
    type: [String, Number],
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入'
  },
  readonly: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  required: {
    type: Boolean,
    default: false
  },
  error: {
    type: Boolean,
    default: false
  },
  errorMessage: {
    type: String,
    default: ''
  },
  labelWidth: {
    type: [String, Number],
    default: '6.2em'
  },
  labelAlign: {
    type: String,
    default: 'left'
  },
  inputAlign: {
    type: String,
    default: 'left'
  },
  type: {
    type: String,
    default: 'text'
  },
  maxlength: {
    type: [String, Number],
    default: undefined
  },
  rows: {
    type: [String, Number],
    default: 1
  },
  autosize: {
    type: [Boolean, Object],
    default: false
  },
  clearable: {
    type: Boolean,
    default: false
  },
  clickable: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'update:modelValue',
  'input',
  'focus',
  'blur',
  'click',
  'clear'
])

const fieldValue = ref(props.modelValue)
const isFocused = ref(false)

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  fieldValue.value = newVal
})

// 监听内部值变化
watch(fieldValue, (newVal) => {
  emit('update:modelValue', newVal)
})

// 事件处理
const handleInput = (value) => {
  emit('input', value)
}

const handleFocus = (event) => {
  isFocused.value = true
  emit('focus', event)
}

const handleBlur = (event) => {
  isFocused.value = false
  emit('blur', event)
}

const handleClick = (event) => {
  emit('click', event)
}

const handleClear = (event) => {
  emit('clear', event)
}
</script>

<style scoped lang="less">
.wo-form-item {
  position: relative;
  margin-bottom: 12px;
  background: @bg-color-white;
  border-radius: @radius-8;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
  border: 1px solid #F5F7FA;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;

  // 悬停效果
  &:hover {
    border-color: fade(@theme-color, 30%);
    box-shadow: 0 2px 8px rgba(255, 122, 10, 0.08);
  }

  // 聚焦状态
  &:focus-within {
    border-color: @theme-color;
    box-shadow: 0 0 0 3px fade(@theme-color, 15%), 0 2px 8px rgba(255, 122, 10, 0.12);
    transform: translateY(-1px);
  }

  // 错误状态
  &.error {
    border-color: @color-red;
    box-shadow: 0 0 0 3px fade(@color-red, 15%);
    animation: shake 0.5s ease-in-out;
  }

  // 禁用状态
  &.disabled {
    background: #FAFBFC;
    border-color: #E8EAED;
    opacity: 0.7;
    cursor: not-allowed;
  }

  :deep(.van-field) {
    padding: 16px 20px;
    border-bottom: none;
    background: transparent;
    min-height: 56px;

    .van-field__label {
      font-size: @font-size-15;
      color: @text-color-primary;
      font-weight: @font-weight-500;
      min-width: 80px;
      margin-right: 16px;
      width: auto;
      position: relative;

      // 必填标记优化
      &::before {
        content: '';
        position: absolute;
        left: -8px;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 3px;
        border-radius: 50%;
        background: @color-red;
        opacity: 0;
        transition: opacity 0.2s ease;
      }
    }

    // 必填字段标记
    &.van-field--required .van-field__label::before {
      opacity: 1;
    }

    .van-field__control {
      font-size: @font-size-15;
      color: @text-color-primary;
      line-height: 1.5;
      transition: color 0.2s ease;

      &::placeholder {
        color: @text-color-tertiary;
        opacity: 1;
        font-size: @font-size-14;
      }

      // 聚焦时输入框文字颜色加深
      &:focus {
        color: @text-color-primary;
      }
    }

    .van-field__body {
      align-items: center;
      min-height: 24px;
    }

    // 移除默认边框
    &::after {
      display: none;
    }

    // 错误信息样式优化
    .van-field__error-message {
      font-size: @font-size-12;
      color: @color-red;
      margin-top: 4px;
      padding-left: 96px;
      animation: slideDown 0.3s ease;
    }
  }

  &-arrow {
    cursor: pointer;

    &:active {
      transform: scale(0.98);
    }

    :deep(.van-field) {
      .van-field__body {
        display: flex;
        align-items: center;
      }
    }
  }
}

// 现代化箭头图标
.arrow-icon {
  width: 8px;
  height: 14px;
  margin-left: 8px;
  opacity: 0.6;
  transition: all 0.2s ease;
  filter: brightness(0) saturate(100%) invert(42%) sepia(8%) saturate(1168%) hue-rotate(185deg) brightness(95%) contrast(89%);

  .wo-form-item:hover & {
    opacity: 1;
    transform: translateX(2px);
  }
}

// 动画定义
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-4px); }
  75% { transform: translateX(4px); }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式优化
@media (max-width: 375px) {
  .wo-form-item {
    margin-bottom: 8px;

    :deep(.van-field) {
      padding: 14px 16px;
      min-height: 52px;

      .van-field__label {
        min-width: 70px;
        margin-right: 12px;
        font-size: @font-size-14;
      }

      .van-field__control {
        font-size: @font-size-14;
      }
    }
  }
}

// 深色模式支持（预留）
@media (prefers-color-scheme: dark) {
  .wo-form-item {
    background: #1F2937;
    border-color: #374151;

    :deep(.van-field) {
      .van-field__label {
        color: #F9FAFB;
      }

      .van-field__control {
        color: #F9FAFB;

        &::placeholder {
          color: #9CA3AF;
        }
      }
    }
  }
}
</style>
